

<?php $__env->startSection('title', 'Tạo tài khoản Premium - Shop Thời Trang Luxury'); ?>
<?php $__env->startSection('description', 'Tạo tài khoản Premium miễn phí tại Shop Thời Trang để trải nghiệm mua sắm cao cấp với ưu đãi độc quyền, dịch vụ cá nhân hóa và chăm sóc VIP 24/7.'); ?>
<?php $__env->startSection('keywords', 'đăng ký premium, tạo tài khoản luxury, register VIP, shop thời trang cao cấp, thành viên premium'); ?>

<?php $__env->startSection('content'); ?>
<!-- Premium Loading Screen -->
<div id="loadingScreen" class="loading-screen">
    <img src="<?php echo e(asset('img/logo.png')); ?>" alt="Shop Thời Trang Premium Logo" class="loading-logo">
    <div class="loading-spinner"></div>
</div>

<main class="auth-container" role="main">
    <article class="auth-card">
        <!-- Premium Header Section -->
        <header class="auth-header">
            <img src="<?php echo e(asset('img/logo.png')); ?>" alt="Shop Thời Trang Premium" class="auth-logo">
            <h1 class="auth-title">Tạo tài khoản Premium</h1>
            <p class="auth-subtitle">Gia nhập cộng đồng thời trang cao cấp - Hoàn toàn miễn phí</p>
        </header>

        <!-- Alert Messages -->
        <?php if(Session::has('message')): ?>
            <div class="alert alert-info" role="alert">
                <i class="fa fa-info-circle" aria-hidden="true"></i>
                <?php echo e(Session::get('message')); ?>

            </div>
            <?php Session::put('message', null); ?>
        <?php endif; ?>

        <?php if(Session::has('success')): ?>
            <div class="alert alert-success" role="alert">
                <i class="fa fa-check-circle" aria-hidden="true"></i>
                <?php echo e(Session::get('success')); ?>

            </div>
        <?php endif; ?>

        <?php if($errors->any()): ?>
            <div class="alert alert-error" role="alert">
                <i class="fa fa-exclamation-circle" aria-hidden="true"></i>
                <ul style="margin: 0; padding-left: 1rem;">
                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li><?php echo e($error); ?></li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- Registration Form -->
        <form action="<?php echo e(route('user.cus_register')); ?>" method="post" id="registerForm" novalidate>
            <?php echo csrf_field(); ?>

            <!-- Premium Full Name Field -->
            <div class="form-group">
                <label for="name" class="form-label required">Họ và tên Premium</label>
                <div class="input-group">
                    <input
                        type="text"
                        id="name"
                        name="name"
                        class="form-input <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                        placeholder="VD: Nguyễn Văn Premium"
                        value="<?php echo e(old('name')); ?>"
                        required
                        autocomplete="name"
                        aria-describedby="name-error"
                    >
                    <i class="fa fa-crown input-icon" aria-hidden="true"></i>
                </div>
                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="error-message" id="name-error" role="alert">
                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Premium Email Field -->
            <div class="form-group">
                <label for="email" class="form-label required">Email Premium</label>
                <div class="input-group">
                    <input
                        type="email"
                        id="email"
                        name="email"
                        class="form-input <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                        placeholder="<EMAIL>"
                        value="<?php echo e(old('email')); ?>"
                        required
                        autocomplete="email"
                        aria-describedby="email-error"
                    >
                    <i class="fa fa-envelope input-icon" aria-hidden="true"></i>
                </div>
                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="error-message" id="email-error" role="alert">
                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Premium Password Field -->
            <div class="form-group">
                <label for="password" class="form-label required">Mật khẩu bảo mật Premium</label>
                <div class="input-group">
                    <input
                        type="password"
                        id="password"
                        name="password"
                        class="form-input <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                        placeholder="••••••••••••••••"
                        required
                        autocomplete="new-password"
                        aria-describedby="password-error password-help"
                        minlength="8"
                    >
                    <i class="fa fa-shield-alt input-icon" aria-hidden="true"></i>
                    <button type="button" class="password-toggle" onclick="togglePassword('password')" aria-label="Hiển thị/Ẩn mật khẩu">
                        <i class="fa fa-eye" aria-hidden="true"></i>
                    </button>
                </div>
                <small id="password-help" style="font-size: 0.85rem; color: rgba(255, 255, 255, 0.6); margin-top: 0.5rem; display: block;">
                    <i class="fa fa-info-circle" aria-hidden="true"></i>
                    Mật khẩu Premium: tối thiểu 8 ký tự, bao gồm chữ hoa, thường, số và ký tự đặc biệt
                </small>
                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="error-message" id="password-error" role="alert">
                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Premium Confirm Password Field -->
            <div class="form-group">
                <label for="password_confirmation" class="form-label required">Xác nhận mật khẩu Premium</label>
                <div class="input-group">
                    <input
                        type="password"
                        id="password_confirmation"
                        name="password_confirmation"
                        class="form-input <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                        placeholder="••••••••••••••••"
                        required
                        autocomplete="new-password"
                        aria-describedby="password-confirmation-error"
                    >
                    <i class="fa fa-check-circle input-icon" aria-hidden="true"></i>
                    <button type="button" class="password-toggle" onclick="togglePassword('password_confirmation')" aria-label="Hiển thị/Ẩn mật khẩu xác nhận">
                        <i class="fa fa-eye" aria-hidden="true"></i>
                    </button>
                </div>
                <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="error-message" id="password-confirmation-error" role="alert">
                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Premium Phone Field -->
            <div class="form-group">
                <label for="phone" class="form-label required">Số điện thoại Premium</label>
                <div class="input-group">
                    <input
                        type="tel"
                        id="phone"
                        name="phone"
                        class="form-input <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                        placeholder="0901 234 567"
                        value="<?php echo e(old('phone')); ?>"
                        required
                        autocomplete="tel"
                        aria-describedby="phone-error"
                        pattern="[0-9]{10,11}"
                    >
                    <i class="fa fa-mobile-alt input-icon" aria-hidden="true"></i>
                </div>
                <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="error-message" id="phone-error" role="alert">
                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Premium Address Field -->
            <div class="form-group">
                <label for="address" class="form-label required">Địa chỉ giao hàng Premium</label>
                <div class="input-group">
                    <input
                        type="text"
                        id="address"
                        name="address"
                        class="form-input <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                        placeholder="123 Đường ABC, Quận XYZ, TP. HCM"
                        value="<?php echo e(old('address')); ?>"
                        required
                        autocomplete="street-address"
                        aria-describedby="address-error"
                    >
                    <i class="fa fa-map-marker-alt input-icon" aria-hidden="true"></i>
                </div>
                <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="error-message" id="address-error" role="alert">
                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Premium Terms and Conditions -->
            <div class="checkbox-group">
                <input type="checkbox" id="terms" name="terms" class="checkbox-input" required>
                <label for="terms" class="checkbox-label">
                    <i class="fa fa-shield-check" aria-hidden="true"></i>
                    Tôi đồng ý với
                    <a href="#" class="form-link" onclick="showTermsModal(); return false;">
                        <strong>Điều khoản Premium</strong>
                    </a>
                    và
                    <a href="#" class="form-link" onclick="showPrivacyModal(); return false;">
                        <strong>Chính sách bảo mật VIP</strong>
                    </a>
                </label>
            </div>

            <!-- Premium Submit Button -->
            <button type="submit" class="btn btn-primary" id="registerBtn">
                <i class="fa fa-crown" aria-hidden="true"></i>
                Tạo tài khoản Premium miễn phí
            </button>

            <!-- Premium Navigation -->
            <nav class="form-nav" role="navigation">
                <div class="form-nav-links">
                    <a href="<?php echo e(route('home.index')); ?>" class="form-link">
                        <i class="fa fa-home" aria-hidden="true"></i>
                        Về trang chủ Premium
                    </a>
                </div>

                <p style="margin-top: 1.5rem; font-size: 1rem;">
                    Đã có tài khoản Premium?
                    <a href="<?php echo e(route('user.cus_login')); ?>" class="form-link" style="font-weight: 700; font-size: 1.1rem;">
                        <i class="fa fa-sign-in-alt" aria-hidden="true"></i>
                        Đăng nhập ngay
                    </a>
                </p>
            </nav>
        </form>
    </article>
</main>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\doannhomD\shopthoitrang\resources\views/cus_register.blade.php ENDPATH**/ ?>